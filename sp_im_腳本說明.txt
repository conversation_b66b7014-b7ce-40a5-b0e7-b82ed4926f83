SP IM 發包腳本說明(與sstmk等服務相同)
機器: sp-log
腳本位置: /home/<USER>/up-apps

服務列表:
  "
  im-tcp
  im-service
  im-message-store
  " 
1.查看im服務
指令: sh 07-status-service.sh 服務
範例: sh 07-status-service.sh  im-service


2.重啟im服務
指令: sh 06-restart-service.sh  服務
範例: sh 06-restart-service.sh  im-tcp

3.解壓縮上傳包
目錄格式: 202506240958.tar 
202506240958.tar
└── 202506240958
    └── im-tcp.jar

指令: sh 01-download-zip.sh  壓縮包名稱
範例: sh 01-download-zip.sh  202506240958.tar 

4.發包
指令: sh 02-update-service.sh  服務
範例: sh 02-update-service.sh  im-tcp




sh 01-download-zip.sh 202507031525.zip

sh 02-update-service.sh  im-tcp
sh 02-update-service.sh  im-service
sh 02-update-service.sh  im-message-store




# 1. 只上传打包后的JAR文件

scp "D:\deploy_package\202507071936.zip" eric@***************:/home/<USER>/work



cd apps-zip

登录远程服务器
ssh ***************

unzip 202507071936.zip


zip -er 202507071936.zip 202507071936





sh ftp.sh
sp-dev / UoPRndY4wU@Z3^H2wqY

cd apps-zip

put 202507081759.zip


ls -R | grep "202507071936.zip"

然后把要发布的zip jar 上传上去

接着 退出 ftp


发布：

sh log.sh

cd up-apps

sh 01-download-zip.sh 202507181829.zip
sh 02-update-service.sh  im-tcp
sh 02-update-service.sh  im-service
sh 02-update-service.sh  im-message-store




Sun Okay, [2025/7/3/周四 20:56]
sh im.sh

Sun Okay, [2025/7/3/周四 20:57]
Nmu<d+RkVb^G9@J6



Sun Okay, [2025/7/3/周四 18:45]
sh log.sh

Sun Okay, [2025/7/3/周四 18:45]
cd /var/sp/logs/sst

Sun Okay, [2025/7/3/周四 20:56]
sh im.sh

Sun Okay, [2025/7/3/周四 20:57]
Nmu<d+RkVb^G9@J6




mysql -u root -p -h ************ im-core < /home/<USER>/work/fix_emoji_charset.sql



scp "D:\deploy_package\fix_emoji_charset.sql" eric@***************:/home/<USER>/work
scp "D:\deploy_package\ftp_upload.sh" eric@***************:/home/<USER>/work



202507081759






# 打包所有模块
.\deploy_package.ps1

# 打包单个模块
.\deploy_package.ps1 im-service

# 打包多个模块
.\deploy_package.ps1 im-tcp im-service

登录远程服务器
ssh ***************




# 上传默认文件
./ftp_upload.sh

# 上传指定文件
./ftp_upload.sh 202507212013.zip

# 查看帮助
./ftp_upload.sh --help



sh log.sh

cd up-apps

sh 01-download-zip.sh 202508011242.zip
sh 02-update-service.sh  im-tcp
sh 02-update-service.sh  im-service
sh 02-update-service.sh  im-message-store

sh 07-status-service.sh  im-service



