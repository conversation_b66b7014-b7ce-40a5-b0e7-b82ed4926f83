package com.lld.im.tcp.reciver;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.lld.im.codec.proto.Message;
import com.lld.im.codec.proto.MessagePack;
import com.lld.im.common.constant.Constants;
import com.lld.im.tcp.handler.BroadcastMessageHandler;
import com.lld.im.tcp.model.BroadcastMessageContent;
import com.lld.im.tcp.utils.MqFactory;
import com.rabbitmq.client.AMQP;
import com.rabbitmq.client.Channel;
import com.rabbitmq.client.DefaultConsumer;
import com.rabbitmq.client.Envelope;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;

/**
 * 系统通知消息接收处理器
 */
@Slf4j
public class SystemNotificationReceiver {
    
    private static final Logger logger = LoggerFactory.getLogger(SystemNotificationReceiver.class);
    private static String brokerId;

    /**
     * 初始化系统通知接收器
     */
    public static void init(String brokerId) {
        SystemNotificationReceiver.brokerId = brokerId;
        initNormalUserNotifications();
        initGuestUserNotifications();
        initAllUserBroadcasts();
        logger.info("系统通知接收器初始化完成，brokerId: {}", brokerId);
    }

    /**
     * 初始化普通用户系统通知队列的消费者
     */
    private static void initNormalUserNotifications() {
        try {
            Channel channel = MqFactory.getChannel(Constants.RabbitConstants.SystemNotification + ".normal");
            // 先声明交换机
            channel.exchangeDeclare(Constants.RabbitConstants.SystemNotification, "direct", true);
            
            channel.queueDeclare(
                    Constants.RabbitConstants.SystemNotification + ".normal", 
                    true, false, false, null
            );
            channel.queueBind(
                    Constants.RabbitConstants.SystemNotification + ".normal",
                    Constants.RabbitConstants.SystemNotification, 
                    Constants.RabbitConstants.NormalUserRoutingKey
            );

            channel.basicConsume(
                    Constants.RabbitConstants.SystemNotification + ".normal", 
                    false, 
                    new DefaultConsumer(channel) {
                        @Override
                        public void handleDelivery(String consumerTag, Envelope envelope, 
                                                AMQP.BasicProperties properties, byte[] body) throws IOException {
                            try {
                                String msgStr = new String(body, "utf-8");
                                logger.info("收到普通用户系统通知: {}", msgStr);

                                JSONObject jsonObject = JSON.parseObject(msgStr);
                                BroadcastMessageContent broadcastContent = jsonObject.toJavaObject(BroadcastMessageContent.class);

                                // 检查是否为精准推送
                                boolean isTargetedPush = isTargetedPush(broadcastContent);
                                if (isTargetedPush) {
                                    logger.info("检测到精准推送消息");
                                } else {
                                    logger.info("检测到广播推送消息");
                                }

                                // 构建消息包
                                Message message = new Message();
                                MessagePack messagePack = new MessagePack();
                                messagePack.setCommand(broadcastContent.getCommand());
                                messagePack.setData(broadcastContent.getData());
                                messagePack.setIncludeGuests(false); // 普通用户队列不包含游客
                                messagePack.setOnlyForGuests(false);
                                message.setMessagePack(messagePack);

                                // 通过广播处理器发送给普通用户
                                BroadcastMessageHandler.handle(message);

                                channel.basicAck(envelope.getDeliveryTag(), false);
                            } catch (Exception e) {
                                logger.error("处理普通用户系统通知异常", e);
                                channel.basicNack(envelope.getDeliveryTag(), false, true);
                            }
                        }
                    }
            );
            logger.info("普通用户系统通知队列消费者初始化完成");
        } catch (Exception e) {
            logger.error("初始化普通用户系统通知队列消费者失败", e);
        }
    }

    /**
     * 初始化游客系统通知队列的消费者
     */
    private static void initGuestUserNotifications() {
        try {
            Channel channel = MqFactory.getChannel(Constants.RabbitConstants.SystemNotification + ".guest");
            // 先声明交换机
            channel.exchangeDeclare(Constants.RabbitConstants.SystemNotification, "direct", true);
            
            channel.queueDeclare(
                    Constants.RabbitConstants.SystemNotification + ".guest", 
                    true, false, false, null
            );
            channel.queueBind(
                    Constants.RabbitConstants.SystemNotification + ".guest",
                    Constants.RabbitConstants.SystemNotification, 
                    Constants.RabbitConstants.GuestUserRoutingKey
            );

            channel.basicConsume(
                    Constants.RabbitConstants.SystemNotification + ".guest", 
                    false, 
                    new DefaultConsumer(channel) {
                        @Override
                        public void handleDelivery(String consumerTag, Envelope envelope, 
                                                AMQP.BasicProperties properties, byte[] body) throws IOException {
                            try {
                                String msgStr = new String(body, "utf-8");
                                logger.info("收到游客系统通知: {}", msgStr);
                                
                                JSONObject jsonObject = JSON.parseObject(msgStr);
                                BroadcastMessageContent broadcastContent = jsonObject.toJavaObject(BroadcastMessageContent.class);
                                
                                // 构建消息包
                                Message message = new Message();
                                @SuppressWarnings({"rawtypes", "unchecked"})
                                MessagePack messagePack = new MessagePack();
                                messagePack.setCommand(broadcastContent.getCommand());
                                messagePack.setData(broadcastContent.getData());
                                messagePack.setIncludeGuests(broadcastContent.getIncludeGuests());
                                messagePack.setOnlyForGuests(broadcastContent.getOnlyForGuests());
                                message.setMessagePack(messagePack);
                                
                                // 直接通过广播处理器发送给游客
                                broadcastContent.setOnlyForGuests(true);
                                BroadcastMessageHandler.handle(message);
                                
                                channel.basicAck(envelope.getDeliveryTag(), false);
                            } catch (Exception e) {
                                logger.error("处理游客系统通知异常", e);
                                channel.basicNack(envelope.getDeliveryTag(), false, true);
                            }
                        }
                    }
            );
            logger.info("游客系统通知队列消费者初始化完成");
        } catch (Exception e) {
            logger.error("初始化游客系统通知队列消费者失败", e);
        }
    }

    /**
     * 初始化所有用户广播消息队列的消费者
     */
    private static void initAllUserBroadcasts() {
        try {
            Channel channel = MqFactory.getChannel(Constants.RabbitConstants.BroadcastMessage + ".all");
            // 先声明交换机
            channel.exchangeDeclare(Constants.RabbitConstants.BroadcastMessage, "direct", true);
            
            channel.queueDeclare(
                    Constants.RabbitConstants.BroadcastMessage + ".all", 
                    true, false, false, null
            );
            channel.queueBind(
                    Constants.RabbitConstants.BroadcastMessage + ".all",
                    Constants.RabbitConstants.BroadcastMessage, 
                    Constants.RabbitConstants.AllUserRoutingKey
            );

            channel.basicConsume(
                    Constants.RabbitConstants.BroadcastMessage + ".all", 
                    false, 
                    new DefaultConsumer(channel) {
                        @Override
                        public void handleDelivery(String consumerTag, Envelope envelope, 
                                                AMQP.BasicProperties properties, byte[] body) throws IOException {
                            try {
                                String msgStr = new String(body, "utf-8");
                                logger.info("收到广播消息: {}", msgStr);
                                
                                JSONObject jsonObject = JSON.parseObject(msgStr);
                                BroadcastMessageContent broadcastContent = jsonObject.toJavaObject(BroadcastMessageContent.class);
                                
                                // 构建消息包
                                Message message = new Message();
                                @SuppressWarnings({"rawtypes", "unchecked"})
                                MessagePack messagePack = new MessagePack();
                                messagePack.setCommand(broadcastContent.getCommand());
                                messagePack.setData(broadcastContent.getData());
                                messagePack.setIncludeGuests(broadcastContent.getIncludeGuests());
                                messagePack.setOnlyForGuests(broadcastContent.getOnlyForGuests());
                                message.setMessagePack(messagePack);
                                
                                // 直接通过广播处理器发送给所有用户
                                broadcastContent.setIncludeGuests(true);
                                BroadcastMessageHandler.handle(message);
                                
                                channel.basicAck(envelope.getDeliveryTag(), false);
                            } catch (Exception e) {
                                logger.error("处理广播消息异常", e);
                                channel.basicNack(envelope.getDeliveryTag(), false, true);
                            }
                        }
                    }
            );
            logger.info("所有用户广播消息队列消费者初始化完成");
        } catch (Exception e) {
            logger.error("初始化所有用户广播消息队列消费者失败", e);
        }
    }

    /**
     * 检查是否为精准推送（包含receiverIds）
     */
    private static boolean isTargetedPush(BroadcastMessageContent broadcastContent) {
        try {
            Object data = broadcastContent.getData();
            if (data instanceof JSONObject) {
                JSONObject dataJson = (JSONObject) data;
                Object receiverIds = dataJson.get("receiverIds");
                return receiverIds != null && receiverIds instanceof java.util.List
                        && !((java.util.List<?>) receiverIds).isEmpty();
            }
        } catch (Exception e) {
            logger.debug("检查精准推送失败", e);
        }
        return false;
    }

    /**
     * 获取接收者数量
     */
    private static int getReceiverCount(BroadcastMessageContent broadcastContent) {
        try {
            Object data = broadcastContent.getData();
            if (data instanceof JSONObject) {
                JSONObject dataJson = (JSONObject) data;
                Object receiverIds = dataJson.get("receiverIds");
                if (receiverIds instanceof java.util.List) {
                    return ((java.util.List<?>) receiverIds).size();
                }
            }
        } catch (Exception e) {
            logger.debug("获取接收者数量失败", e);
        }
        return 0;
    }
}